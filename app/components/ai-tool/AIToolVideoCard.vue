<script setup lang="ts">
import { ref, computed, nextTick } from 'vue'

const props = defineProps({
  orientation: {
    type: String as () => 'horizontal' | 'vertical',
    default: 'horizontal'
  },
  videoUrl: {
    type: String,
    default: ''
  },
  thumbnailUrl: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  prompt: {
    type: String,
    default: ''
  },
  preset: {
    type: String,
    default: 'Unknown'
  },
  aspectRatio: {
    type: String,
    default: '16:9'
  },
  duration: {
    type: String,
    default: '8s'
  },
  loading: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: () => ({})
  }
})

const { models, model } = useVideoGenModels()
const { videoDimension } = useVideoDimensions()
const textToVideoStore = useTextToVideoStore()
const { t } = useI18n()

const isFullScreenOpen = ref(false)
const isVideoPlaying = ref(false)

// Find the corresponding model label for the preset value
const presetLabel = computed(() => {
  const modelFound = models.find(m => m.value === props.preset)
  return modelFound ? modelFound.label : props.preset
})

const openFullScreen = () => {
  isFullScreenOpen.value = true
}

const onVideoLoaded = (event: any) => {
  console.log(event?.message || t('ui.messages.videoLoaded'))
}

const onVideoProcessing = (event: any) => {
  console.log(event?.message || t('ui.messages.videoProcessing'))
}

const getPersonGenerationLabel = (value: string) => {
  const options = [
    { value: 'DONT_ALLOW', label: t('personGeneration.dontAllow') },
    { value: 'ALLOW_ADULT', label: t('personGeneration.allowAdult') },
    { value: 'ALLOW_ALL', label: t('personGeneration.allowAll') }
  ]
  return options.find(option => option.value === value)?.label || value
}

const onGenerateWithSettings = () => {
  // Copy the settings from this card to the current form state
  
  // Find and set the model
  const modelFound = models.find(m => m.value === props.preset)
  if (modelFound) {
    model.value = modelFound
  }

  // Set the aspect ratio
  videoDimension.value = props.aspectRatio

  // Set the prompt in the store
  textToVideoStore.prompt = props.prompt

  // Scroll to the top of the page and focus the prompt input
  nextTick(() => {
    // Scroll to top smoothly
    window.scrollTo({ top: 0, behavior: 'smooth' })

    // Try to focus the prompt input after scrolling
    setTimeout(() => {
      // Look for the prompt input (UChatPrompt component)
      const promptInput = document.querySelector(
        '[class*="chat-prompt"] textarea, [class*="chat-prompt"] input'
      )
      if (promptInput && promptInput instanceof HTMLElement) {
        promptInput.focus()
      }
    }, 500)
  })
}
</script>

<template>
  <UPageCard
    :title="title"
    :orientation="orientation"
    spotlight
    spotlight-color="primary"
    :ui="{
      container: 'lg:items-start',
      body: 'w-full h-full',
      wrapper: 'h-full',
      description: 'h-full flex flex-col'
    }"
    class="h-full"
  >
    <USkeleton
      v-if="loading"
      class="w-full h-64 lg:h-80 mb-4"
      :ui="{ container: 'rounded-lg' }"
    />
    <div
      v-else
      class="relative order-first lg:order-last w-full group cursor-pointer"
      @click="openFullScreen"
    >
      <video
        v-if="videoUrl"
        :src="videoUrl"
        :poster="thumbnailUrl"
        class="w-full h-64 lg:h-80 object-cover rounded-lg"
        muted
        loop
        @mouseenter="$event.target.play()"
        @mouseleave="$event.target.pause()"
        @loadeddata="onVideoLoaded"
        @loadstart="onVideoProcessing"
      />
      <img
        v-else-if="thumbnailUrl"
        :src="thumbnailUrl"
        :alt="title"
        class="w-full h-64 lg:h-80 object-cover rounded-lg"
      >
      <div
        v-else
        class="w-full h-64 lg:h-80 bg-gray-200 dark:bg-gray-800 rounded-lg flex items-center justify-center"
      >
        <UIcon
          name="i-lucide-video"
          class="w-16 h-16 text-gray-400"
        />
      </div>
      
      <!-- Play button overlay -->
      <div
        v-if="videoUrl"
        class="absolute inset-0 flex items-center justify-center bg-black/30 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      >
        <UIcon
          name="i-lucide-play"
          class="w-12 h-12 text-white"
        />
      </div>
      
      <!-- Duration badge -->
      <div
        v-if="duration"
        class="absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded"
      >
        {{ duration }}
      </div>
    </div>
    
    <template #title>
      <div class="line-clamp-2">
        {{ title }}
      </div>
    </template>
    
    <template #description>
      <div class="text-xs mt-2">
        <div class="font-bold line-clamp-2">
          <div>{{ $t("promptDetails") }}</div>
        </div>
        <div class="font-light p-2 bg-muted mt-1 rounded-lg">
          {{ prompt }}
        </div>
      </div>
      <BaseInfo
        class="mt-4"
        :properties="{
          model: presetLabel,
          aspectRatio: data?.aspect_ratio || aspectRatio,
          duration: duration,
          personGeneration: getPersonGenerationLabel(data?.person_generation),
          used_credit: data?.used_credit
        }"
      />
      <div
        v-if="!loading"
        class="flex flex-row gap-4 items-center mt-6"
      >
        <UChatPromptSubmit
          color="primary"
          :label="$t('generateWithSettings')"
          class="cursor-pointer w-full justify-center bg-gradient-to-r from-primary-500 to-violet-500 max-h-10 dark:text-white hover:from-primary-600 hover:to-violet-600"
          icon="mingcute:ai-fill"
          @click="onGenerateWithSettings"
        />
      </div>
    </template>
  </UPageCard>

  <!-- Full Screen Video Modal -->
  <UModal
    v-model:open="isFullScreenOpen"
    fullscreen
    :ui="{
      content: 'bg-black/90 backdrop-blur-xl'
    }"
    @keydown.esc="isFullScreenOpen = false"
  >
    <template #content>
      <div
        class="relative w-full h-full flex items-center justify-center"
        @click="isFullScreenOpen = false"
      >
        <!-- Prevent click propagation on the video itself to avoid closing when clicking on the video -->
        <video
          v-if="videoUrl"
          :src="videoUrl"
          :poster="thumbnailUrl"
          class="max-h-[90vh] max-w-[90vw] object-contain cursor-auto"
          controls
          autoplay
          @click.stop
          @play="isVideoPlaying = true"
          @pause="isVideoPlaying = false"
        />
        <img
          v-else-if="thumbnailUrl"
          :src="thumbnailUrl"
          :alt="title"
          class="max-h-[90vh] max-w-[90vw] object-contain cursor-zoom-out"
          @click.stop
        >
        <UButton
          icon="i-lucide-x"
          color="neutral"
          variant="ghost"
          class="absolute top-4 right-4 text-white hover:bg-white/10"
          @click="isFullScreenOpen = false"
        />
        <div class="absolute bottom-4 text-white/70 text-sm">
          {{ $t("clickToClose") }}
        </div>
      </div>
    </template>
  </UModal>
</template>
