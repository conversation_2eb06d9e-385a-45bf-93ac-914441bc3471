import { createSharedComposable } from '@vueuse/core'

export interface ErrorContext {
  action: string
  component?: string
  userId?: string
  timestamp?: Date
  metadata?: Record<string, any>
}

export interface ErrorHandlerOptions {
  showToast?: boolean
  logToConsole?: boolean
  reportToService?: boolean
  customMessage?: string
}

const _useErrorHandler = () => {
  const toast = useToast()
  // const { t } = useI18n()
  const $nuxtApp = useNuxtApp()
  const t = $nuxtApp.$i18n.t

  const defaultOptions: ErrorHandlerOptions = {
    showToast: true,
    logToConsole: true,
    reportToService: false
  }

  const getErrorMessage = (error: any): string => {
    if (typeof error === 'string') return error
    if (error?.message) return error.message
    if (error?.response?.data?.message) return error.response.data.message
    if (error?.response?.data?.error) return error.response.data.error
    return t('An unexpected error occurred')
  }

  const getErrorCode = (error: any): string | undefined => {
    if (error?.code) return error.code
    if (error?.response?.status) return `HTTP_${error.response.status}`
    if (error?.name) return error.name
    return undefined
  }

  const logError = (error: any, context: ErrorContext) => {
    const errorInfo = {
      message: getErrorMessage(error),
      code: getErrorCode(error),
      stack: error?.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    console.error('Application Error:', errorInfo)

    // You can extend this to send to external logging service
    // if (context.reportToService) {
    //   sendToLoggingService(errorInfo)
    // }
  }

  const showErrorToast = (error: any, customMessage?: string) => {
    const message = customMessage || getErrorMessage(error)
    const code = getErrorCode(error)

    toast.add({
      id: `error-${Date.now()}`,
      title: t('Error'),
      description: code ? `${message} (${code})` : message,
      color: 'error',
      timeout: 5000
    })
  }

  const handleError = (
    error: any,
    context: ErrorContext,
    options: Partial<ErrorHandlerOptions> = {}
  ) => {
    const finalOptions = { ...defaultOptions, ...options }

    if (finalOptions.logToConsole) {
      logError(error, context)
    }

    if (finalOptions.showToast) {
      showErrorToast(error, finalOptions.customMessage)
    }

    // Return formatted error info for further processing if needed
    return {
      message: getErrorMessage(error),
      code: getErrorCode(error),
      context,
      timestamp: new Date()
    }
  }

  // Specific error handlers for common scenarios
  const handleGenerationError = (
    error: any,
    generationType: string,
    options: Partial<ErrorHandlerOptions> = {}
  ) => {
    return handleError(error, {
      action: 'generation',
      component: `${generationType}-gen`,
      metadata: { generationType }
    }, {
      customMessage: t('Failed to generate {type}. Please try again.', { type: generationType }),
      ...options
    })
  }

  const handleValidationError = (
    error: any,
    field: string,
    options: Partial<ErrorHandlerOptions> = {}
  ) => {
    return handleError(error, {
      action: 'validation',
      metadata: { field }
    }, {
      customMessage: t('Validation failed for {field}', { field }),
      ...options
    })
  }

  const handleApiError = (
    error: any,
    endpoint: string,
    options: Partial<ErrorHandlerOptions> = {}
  ) => {
    return handleError(error, {
      action: 'api_call',
      metadata: { endpoint }
    }, {
      customMessage: t('API request failed. Please check your connection and try again.'),
      ...options
    })
  }

  const handleFileError = (
    error: any,
    operation: string,
    filename?: string,
    options: Partial<ErrorHandlerOptions> = {}
  ) => {
    return handleError(error, {
      action: 'file_operation',
      metadata: { operation, filename }
    }, {
      customMessage: t('File operation failed: {operation}', { operation }),
      ...options
    })
  }

  // Global error handler for unhandled promise rejections
  const setupGlobalErrorHandling = () => {
    window.addEventListener('unhandledrejection', (event) => {
      handleError(event.reason, {
        action: 'unhandled_promise_rejection',
        component: 'global'
      })
    })

    window.addEventListener('error', (event) => {
      handleError(event.error, {
        action: 'unhandled_error',
        component: 'global',
        metadata: {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      })
    })
  }

  return {
    handleError,
    handleGenerationError,
    handleValidationError,
    handleApiError,
    handleFileError,
    setupGlobalErrorHandling,
    getErrorMessage,
    getErrorCode
  }
}

export const useErrorHandler = createSharedComposable(_useErrorHandler)
