{"system": "Générateur d'Images IA", "helloWorld": "Bonjour le monde !", "Describe the image you want to generate...": "Décrivez l'image que vous voulez générer...", "appTitle": "Création d'Images IA", "copyright": "Copyright © {year}, Création d'Images IA", "available": "Disponible pour de nouveaux projets", "notAvailable": "Non disponible pour le moment", "blog": "Blog", "copyLink": "Copier le lien", "minRead": "MIN LECTURE", "articleLinkCopied": "Lien de l'article copié dans le presse-papiers", "clickToClose": "Cliquez n'importe où ou appuyez sur ESC pour fermer", "promptDetails": "<PERSON><PERSON><PERSON> du prompt", "generateWithPrompt": "Générer avec ce prompt", "generateWithSettings": "Générer avec ces paramètres", "preset": "Préréglage", "style": "Style", "resolution": "Résolution", "addImage": "Ajouter une Image", "modelPreset": "Modèle/Préréglage", "imageDimensions": "Dimensions de l'Image", "yourImage": "Votre Image", "generate": "<PERSON><PERSON><PERSON><PERSON>", "nav.aitool": "Outil IA", "nav.api": "API", "nav.login": "Connexion", "nav.history": "Histoire", "nav.orders": "Commandes", "3D Render": "Rendu 3D", "Acrylic": "Acrylique", "Anime General": "<PERSON><PERSON>", "Creative": "<PERSON><PERSON><PERSON><PERSON>", "Dynamic": "Dynamique", "Fashion": "Mode", "Game Concept": "Concept de Jeu", "Graphic Design 3D": "Design Graphique 3D", "Illustration": "Illustration", "None": "Aucun", "Portrait": "Portrait", "Portrait Cinematic": "Portrait Cinématographique", "Portrait Fashion": "Portrait de Mode", "Ray Traced": "<PERSON><PERSON><PERSON>", "Stock Photo": "Photo de Stock", "Watercolor": "<PERSON><PERSON><PERSON><PERSON>", "AI Image Generator": "Générateur d'Images IA", "Generate AI images from text prompts with a magical particle transformation effect": "Générez des images IA à partir de prompts texte avec un effet magique de transformation de particules", "Enter your prompt": "Entrez votre prompt", "Generating...": "Génération...", "Generate Image": "Générer l'Image", "Enter a prompt and click Generate Image to create an AI image": "Entrez un prompt et cliquez sur Générer l'Image pour créer une image IA", "Prompt:": "Prompt :", "Download": "Télécharger", "How It Works": "Comment ça Marche", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "Ce générateur d'images IA utilise un effet de transformation basé sur les particules pour visualiser le processus de création. Lorsque vous entrez un prompt et cliquez sur 'Générer', le système :", "Sends your prompt to an AI image generation API": "Envoie votre prompt à une API de génération d'images IA", "Creates a particle system with thousands of tiny particles": "Crée un système de particules avec des milliers de petites particules", "Transforms the random noise particles into the generated image": "Transforme les particules de bruit aléatoire en image générée", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "Les particules commencent dans un motif de bruit aléatoire puis se transforment en douceur en image finale, créant un effet magique qui simule le processus créatif de l'IA.", "Transform": "Transformer", "Transforming...": "Transformation...", "Initializing particles...": "Initialisation des particules...", "Loading image...": "Chargement de l'image...", "Creating particle system...": "Création du système de particules...", "Adding event listeners...": "Ajout des écouteurs d'événements...", "Ready!": "<PERSON><PERSON><PERSON><PERSON> !", "AI Image Particle Effect": "Effet de Particules d'Image IA", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "Une démonstration du composant BaseMagicImage qui transforme les particules en images générées par IA", "Click anywhere or press ESC to close": "Cliquez n'importe où ou appuyez sur ESC pour fermer", "auth.login": "Connexion", "auth.loginDescription": "Connectez-vous à votre compte pour continuer", "auth.email": "Email", "auth.enterEmail": "Entrez votre email", "auth.password": "Mot de passe", "auth.enterPassword": "Entrez votre mot de passe", "auth.rememberMe": "Se souvenir de moi", "auth.welcomeBack": "Bon retour", "auth.signupFailed": "Inscription échouée", "auth.signupFailedDescription": "Il y a eu une erreur lors de l'inscription. Veuillez réessayer.", "auth.dontHaveAccount": "Vous n'avez pas de compte ?", "auth.signUp": "S'inscrire", "auth.forgotPassword": "Mot de passe oublié ?", "auth.bySigningIn": "En vous connectant, vous acceptez nos", "auth.termsOfService": "Conditions de Service", "auth.signUpTitle": "S'inscrire", "auth.signUpDescription": "C<PERSON>ez un compte pour commencer", "auth.name": "Nom", "auth.enterName": "Entrez votre nom", "auth.createAccount": "<PERSON><PERSON><PERSON> un compte", "auth.alreadyHaveAccount": "Vous avez déjà un compte ?", "auth.bySigningUp": "En vous inscrivant, vous acceptez nos", "auth.backToHome": "Retour à l'accueil", "auth.notVerifyAccount": "Votre compte n'est pas vérifié. Veuillez vérifier votre compte pour continuer", "auth.verifyAccount": "Vérifier le compte", "auth.resendActivationEmail": "Renvoyer l'email d'activation", "auth.accountRecovery": "Récupération de Compte", "auth.accountRecoveryTitle": "Récupérez votre compte", "auth.accountRecoveryDescription": "Entrez votre email pour recevoir des instructions de réinitialisation de mot de passe", "auth.sendRecoveryEmail": "Envoyer l'email de récupération", "auth.recoveryEmailSent": "Email de récupération envoyé", "auth.recoveryEmailSentDescription": "Veuillez vérifier votre email pour les instructions de réinitialisation de mot de passe", "auth.resetPassword": "Réinitialiser le Mot de Passe", "auth.resetPasswordTitle": "Réinitialisez votre mot de passe", "auth.resetPasswordDescription": "Entrez votre nouveau mot de passe", "auth.newPassword": "Nouveau mot de passe", "auth.confirmPassword": "Confirmer le mot de passe", "auth.enterNewPassword": "Entrez votre nouveau mot de passe", "auth.enterConfirmPassword": "Confirmez votre nouveau mot de passe", "auth.passwordResetSuccess": "Réinitialisation de mot de passe réussie", "auth.passwordResetSuccessDescription": "Votre mot de passe a été réinitialisé avec succès. Vous pouvez maintenant vous connecter avec votre nouveau mot de passe", "auth.activateAccount": "<PERSON><PERSON>", "auth.activateAccountTitle": "Activez votre compte", "auth.activateAccountDescription": "Votre compte est en cours d'activation...", "auth.accountActivated": "Compte activé", "auth.accountActivatedDescription": "Votre compte a été activé avec succès. Vous pouvez maintenant vous connecter", "auth.activationFailed": "Activation échouée", "auth.activationFailedDescription": "Échec de l'activation de votre compte. Veuillez réessayer ou contacter le support", "auth.backToLogin": "Retour à la connexion", "auth.loginFailed": "Échec de la connexion", "auth.loginWithGoogle": "Se connecter avec Google", "auth.google": "Google", "auth.filter": "Filter", "validation.invalidEmail": "<PERSON><PERSON> invalide", "validation.passwordMinLength": "Le mot de passe doit contenir au moins 8 caractères", "validation.nameRequired": "Le nom est requis", "validation.required": "Ce champ est requis", "validation.passwordsDoNotMatch": "Les mots de passe ne correspondent pas", "imageSelect.pleaseSelectImageFile": "Veuillez sélectionner un fichier image", "imageSelect.selectedImage": "Image sélectionnée", "imageSelect.removeImage": "Supprimer l'image", "pixelReveal.loading": "Chargement de l'image...", "pixelReveal.processing": "Traitement de l'image...", "pixelReveal.revealComplete": "Révélation de l'image terminée", "SIGNIN_WRONG_EMAIL_PASSWORD": "Email ou mot de passe incorrect", "Try again": "<PERSON><PERSON><PERSON><PERSON>", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "Video Gen", "aiToolMenu.speechGen": "Speech Gen", "aiToolMenu.musicGen": "Music Gen", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "Générez des images de haute qualité et détaillées avec un rendu de texte précis pour du contenu visuel créatif.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "Exprimez vos idées comme jamais auparavant — avec Imagen, la créativité n'a pas de limites.", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flash est un outil puissant pour générer des images à partir de prompts texte.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "Plus de contrôle, de cohérence et de créativité que jamais auparavant.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "<PERSON><PERSON><PERSON><PERSON>, rencontrez l'audio. Notre dernier modèle de génération vidéo, conçu pour donner du pouvoir aux cinéastes et aux conteurs.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "Le modèle de synthèse vocale le plus avancé disponible.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "Traitement à grande échelle (ex. multiples pdfs).\nTâches à faible latence et haut volume nécessitant de la réflexion\nCas d'usage agentiques", "aiToolMenu.link": "<PERSON><PERSON>", "aiToolMenu.linkDescription": "U<PERSON><PERSON>z NuxtLink avec des super-pouvoirs.", "aiToolMenu.soon": "Bientôt", "readArticle": "Lire l'Article", "switchToLightMode": "Passer en mode clair", "switchToDarkMode": "Passer en mode sombre", "profile": "Profil", "buyCredits.checkout": "Commande", "buyCredits.checkoutDescription": "Confirmez votre commande puis choisissez votre méthode de paiement.", "buyCredits.orderDetail": "<PERSON><PERSON><PERSON> commande", "buyCredits.credits": "Crédits", "buyCredits.pricePerUnit": "Prix par unité", "buyCredits.totalCredits": "Total des crédits", "buyCredits.totalPrice": "Prix total", "buyCredits.payment": "Paiement", "buyCredits.submit": "So<PERSON><PERSON><PERSON>", "buyCredits.cancel": "Annuler", "pricing.title": "Tarification", "pricing.description": "Choisissez le plan parfait pour vos besoins de génération d'images", "pricing.comingSoon": "Bientôt Disponible", "pricing.comingSoonDescription": "Nos plans tarifaires sont en cours de finalisation. Revenez bientôt pour des mises à jour.", "magicImageDemo.title": "Effet de Particules d'Image IA", "magicImageDemo.description": "Une démonstration du composant BaseMagicImage qui transforme les particules en images générées par IA", "magicImageDemo.image": "Image", "magicImageDemo.aboutTitle": "À Propos de Ce Composant", "magicImageDemo.aboutDescription": "Le composant BaseMagicImage utilise Three.js pour créer un système de particules qui peut se transformer entre des positions aléatoires et une image générée par IA. Les particules bougent avec des effets tourbillonnants et fluides, créant une transformation magique.", "magicImageDemo.featuresTitle": "Fonctionnalités", "magicImageDemo.features.particleRendering": "Rendu d'image basé sur les particules", "magicImageDemo.features.smoothTransitions": "Transitions fluides entre les positions aléatoires des particules et la formation d'image", "magicImageDemo.features.interactiveControls": "Contrôles de caméra interactifs (glisser pour faire tourner, défiler pour zoomer)", "magicImageDemo.features.customizable": "Nombre de particules et durée d'animation personnalisables", "magicImageDemo.features.automatic": "Déclenchement de transformation automatique ou manuel", "magicImageDemo.howItWorksTitle": "Comment ça Marche", "magicImageDemo.howItWorksDescription": "Le composant analyse les pixels d'une image et crée un système de particules 3D où chaque particule représente un pixel. Les pixels plus brillants sont positionnés plus près du spectateur, créant un effet 3D subtil. Les particules sont initialement dispersées au hasard dans l'espace 3D, puis s'animent pour former l'image lorsqu'elles sont déclenchées.", "privacy.title": "Politique de Confidentialité", "privacy.description": "Apprenez comment nous protégeons votre confidentialité et gérons vos données", "privacy.informationWeCollect": "Informations que Nous Collectons", "privacy.informationWeCollectDescription": "Nous collectons les informations que vous nous fournissez directement, comme lorsque vous créez un compte, utilisez nos services ou nous contactez pour du support.", "privacy.howWeUseInformation": "Comment Nous Utilisons Vos Informations", "privacy.howWeUseInformationDescription": "Nous utilisons les informations que nous collectons pour fournir, maintenir et améliorer nos services, traiter les transactions et communiquer avec vous.", "privacy.informationSharing": "Partage d'Informations", "privacy.informationSharingDescription": "Nous ne vendons, n'échangeons ou ne transférons pas vos informations personnelles à des tiers sans votre consentement, sauf comme décrit dans cette politique.", "privacy.dataSecurity": "Sécurité des Données", "privacy.dataSecurityDescription": "Nous mettons en place des mesures de sécurité appropriées pour protéger vos informations personnelles contre l'accès non autorisé, l'altération, la divulgation ou la destruction.", "privacy.contactUs": "Contactez-Nous", "privacy.contactUsDescription": "Si vous avez des questions sur cette Politique de Confidentialité, veuillez nous contacter via nos canaux de support.", "terms.title": "Conditions de Service", "terms.description": "Termes et conditions pour utiliser les services Imagen", "terms.acceptanceOfTerms": "1. Acceptation des Conditions", "terms.acceptanceOfTermsDescription": "En accédant et en utilisant les services Imagen, vous acceptez et convenez d'être lié par les termes et dispositions de cet accord.", "terms.useOfService": "2. Utilisation du Service", "terms.useOfServiceDescription": "Vous acceptez d'utiliser notre service uniquement à des fins légales et conformément à ces Conditions de Service.", "terms.userAccounts": "3. <PERSON><PERSON><PERSON>", "terms.userAccountsDescription": "Vous êtes responsable de maintenir la confidentialité de votre compte et mot de passe.", "terms.intellectualProperty": "4. Propriété Intellectuelle", "terms.intellectualPropertyDescription": "Tout le contenu et les matériaux disponibles sur notre service sont protégés par des droits de propriété intellectuelle.", "terms.termination": "5. <PERSON><PERSON><PERSON><PERSON>", "terms.terminationDescription": "Nous pouvons rés<PERSON>er ou suspendre votre compte et l'accès au service à notre seule discrétion.", "terms.disclaimers": "6. Avertissements", "terms.disclaimersDescription": "Le service est fourni 'tel quel' sans aucune garantie d'aucune sorte.", "terms.contactUsTerms": "Contactez-Nous", "terms.contactUsTermsDescription": "Si vous avez des questions sur ces Conditions de Service, veuillez nous contacter via nos canaux de support.", "Describe the video you want to generate...": "Décrivez la vidéo que vous souhaitez gén<PERSON>rer...", "cancel": "Annuler", "confirm": "Confirmer", "appName": "GeminiGen.AI", "quickTopUp": "Recharge rapide", "customTopUp": "<PERSON><PERSON><PERSON>", "numberOfCredits": "Nombre de crédits", "paypal": "PayPal", "paypalDescription": "Payez en toute sécurité avec votre compte PayPal.", "stripe": "Stripe", "stripeDescription": "Payez en toute sécurité avec Stripe", "debitCreditCard": "<PERSON><PERSON> de <PERSON> ou de cré<PERSON>", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Payer avec des cryptos", "cryptoDescription": "Bitcoin, Ethereum et autres cryptomonnaies", "profileMenu.guide": "Guide", "profileMenu.logo": "Logo", "profileMenu.settings": "Paramètres", "profileMenu.components": "Composants", "loadingMoreItems": "Chargement de plus d'articles...", "promptLabel": "Demande :", "videoExamples": "Exemples vidéo", "videoExamplesDescription": "Explorez ces exemples vidéo avec leurs invites et paramètres. Cliquez sur n'importe quel bouton 'Utiliser cette invite' pour copier l'invite dans votre champ de saisie.", "useThisPrompt": "Util<PERSON>z cette invite", "model": "<PERSON><PERSON><PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON>", "videoTypeSelection": "Sélectionner le type de vidéo", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "notifications.types.video_1.title": "Génération de vidéo en attente", "notifications.types.video_1.description": "La génération de vidéo attend d'être traitée.", "notifications.types.video_2.title": "Génération de vidéo terminée", "notifications.types.video_2.description": "La vidéo a été générée avec succès.", "notifications.types.video_3.title": "Échec de la génération de la vidéo", "notifications.types.video_3.description": "Échec de la génération vidéo", "notifications.types.image_1.title": "Génération d'images en attente", "notifications.types.image_1.description": "La génération d'image attend d'être traitée.", "notifications.types.image_2.title": "Génération d'image terminée", "notifications.types.image_2.description": "L'image a été générée avec succès.", "notifications.types.image_3.title": "Échec de la génération d'image", "notifications.types.image_3.description": "Échec de la génération d'image", "notifications.types.tts_history_1.title": "Génération audio en attente", "notifications.types.tts_history_1.description": "La synthèse vocale est en attente de traitement.", "notifications.types.tts_history_2.title": "Génération audio terminée", "notifications.types.tts_history_2.description": "La synthèse vocale a été générée avec succès.", "notifications.types.tts_history_3.title": "Échec de la génération audio", "notifications.types.tts_history_3.description": "La génération de synthèse vocale a échoué.", "notifications.types.voice_training_1.title": "Entraînement vocal en attente", "notifications.types.voice_training_1.description": "La formation vocale est en attente de traitement.", "notifications.types.voice_training_2.title": "Entraînement vocal terminé", "notifications.types.voice_training_2.description": "La formation du modèle vocal personnalisé s'est terminée avec succès.", "notifications.types.voice_training_3.title": "Échec de la formation vocale", "notifications.types.voice_training_3.description": "L'entraînement vocal a échoué.", "notifications.types.music_1.title": "Génération de Musique en Attente", "notifications.types.music_1.description": "La génération de musique attend d'être traitée.", "notifications.types.music_2.title": "Génération de musique terminée", "notifications.types.music_2.description": "La musique IA a été générée avec succès.", "notifications.types.music_3.title": "La génération de musique a échoué.", "notifications.types.music_3.description": "La génération de musique a échoué.", "notifications.types.speech_1.title": "Génération de discours en attente", "notifications.types.speech_1.description": "Votre demande de génération de discours est en attente de traitement.", "notifications.types.speech_2.title": "Génération de discours terminée", "notifications.types.speech_2.description": "Votre discours a été généré avec succès.", "notifications.types.speech_3.title": "Échec de la génération de discours", "notifications.types.speech_3.description": "La génération de votre discours a échoué. Veuillez réessayer.", "notifications.time.justNow": "À l'instant", "notifications.time.minutesAgo": "Il y a {minutes} minutes", "notifications.time.hoursAgo": "Il y a {hours}h", "notifications.time.yesterday": "<PERSON>er", "notifications.status.processing.title": "Traitement", "notifications.status.processing.description": "Votre demande est en cours de traitement.", "notifications.status.success.title": "<PERSON><PERSON><PERSON><PERSON>", "notifications.status.success.description": "<PERSON><PERSON><PERSON><PERSON> avec succès", "notifications.status.failed.title": "<PERSON><PERSON><PERSON>", "notifications.status.failed.description": "Une erreur s'est produite pendant le traitement", "notifications.status.warning.title": "Avertissement", "notifications.status.warning.description": "Terminé avec avertissements", "notifications.status.pending.title": "En attente", "notifications.status.pending.description": "En attente de traitement", "notifications.status.cancelled.title": "<PERSON><PERSON><PERSON>", "notifications.status.cancelled.description": "La demande a été annulée.", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "Paramètres", "userMenu.profile": "Profil", "userMenu.buyCredits": "Acheter des crédits", "userMenu.settings": "Paramètres", "userMenu.api": "API", "userMenu.logout": "Déconnexion", "userMenu.greeting": "Salut, {name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "<PERSON><PERSON><PERSON><PERSON>", "options.allow": "Autoriser", "options.dontAllow": "Ne pas autoriser", "options.voices": "Voix", "options.pickVoice": "Choisir une voix", "voiceTypes.systemVoices": "Voix système", "voiceTypes.customVoices": "Voix personnalisées", "voiceTypes.premiumVoices": "Voix premium", "voiceTypes.userVoices": "Voix utilisateur", "common.home": "Accueil", "Describe the speech you want to generate...": "Décrivez le discours que vous souhaitez générer...", "listenToSpeech": "Écouter le discours", "generateSimilar": "<PERSON><PERSON><PERSON><PERSON> simi<PERSON>", "voice": "Voix", "emotion": "Émotion", "speed": "Vitesse", "speed_settings": "Paramètres de vitesse", "speed_value": "<PERSON>ur de vitesse", "speed_slider": "Glissière de vitesse", "apply": "Appliquer", "speech_settings": "Paramètres de parole", "current_speed": "Vitesse actuelle", "reset_defaults": "Réinitialiser aux valeurs par défaut", "outputFormat": "Format de sortie", "outputChannel": "Canal de sortie", "selectVoice": "Sélectionner la voix", "selectEmotion": "Sélectionner l'émotion", "selectFormat": "Sélectionner le format", "selectChannel": "Sélectionner le canal", "noVoicesAvailable": "Aucune voix disponible", "noEmotionsAvailable": "Aucune émotion disponible", "searchVoices": "Rechercher des voix...", "searchEmotions": "Recherchez des émotions...", "noVoicesFound": "Aucune voix trouvée", "noEmotionsFound": "Aucune émotion trouvée", "retry": "<PERSON><PERSON><PERSON><PERSON>", "noAudioSample": "Aucun échantillon audio disponible", "Speech Generation Complete": "Génération de discours terminée", "Your speech has been generated successfully": "Votre discours a été généré avec succès.", "history.tabs.imagen": "Image", "history.tabs.video": "Vidéo", "history.tabs.speech": "Discours", "history.tabs.music": "Musique", "history.tabs.history": "Histoire", "orders.title": "Historique des commandes", "orders.description": "Afficher votre historique de transactions et de paiements.", "orders.orderId": "Identifiant de commande", "orders.amount": "<PERSON><PERSON>", "orders.credits": "Crédits", "orders.quantity": "Quantité", "orders.platform": "Plateforme", "orders.externalId": "Identifiant de transaction", "orders.status.completed": "<PERSON><PERSON><PERSON><PERSON>", "orders.status.success": "Su<PERSON>ès", "orders.status.paid": "<PERSON><PERSON>", "orders.status.pending": "En attente", "orders.status.processing": "Traitement", "orders.status.failed": "<PERSON><PERSON><PERSON>", "orders.status.cancelled": "<PERSON><PERSON><PERSON>", "orders.status.error": "<PERSON><PERSON><PERSON>", "orders.empty.title": "Pas encore de commandes.", "orders.empty.description": "Vous n'avez pas encore passé de commande. Achetez des crédits pour commencer à utiliser nos services.", "orders.empty.action": "Acheter des crédits", "orders.endOfList": "Vous avez vu toutes les commandes.", "orders.errors.fetchFailed": "Échec du chargement de l'historique des commandes. Veuillez réessayer.", "orders.meta.title": "Historique des commandes - Imagen AI", "orders.meta.description": "Consultez votre historique de transactions et de paiements sur Imagen AI", "historyPages.imagenDescription": "Pa<PERSON>ourez vos images et œuvres d'art générées par IA.", "historyPages.musicDescription": "Parcourez votre musique et contenu audio générés par l'IA.", "historyPages.speechDescription": "<PERSON><PERSON><PERSON>ez votre contenu discursif et vocal généré par l'IA.", "historyPages.videoDescription": "Parcourez vos vidéos et animations générées par IA.", "historyPages.imagenBreadcrumb": "Image", "historyPages.musicBreadcrumb": "Musique", "historyPages.speechBreadcrumb": "Discours", "historyPages.videoBreadcrumb": "Génération de vidéo", "historyPages.endOfImagesHistory": "Vous êtes arrivé à la fin de l'historique des images.", "historyPages.endOfMusicHistory": "Vous êtes arrivé à la fin de l'histoire de la musique.", "historyPages.endOfSpeechHistory": "Vous êtes arrivé à la fin de l'historique des discours.", "historyPages.endOfVideoHistory": "Vous êtes arrivé à la fin de l'historique des vidéos.", "historyPages.noVideosFound": "Aucune vidéo trouvée.", "historyPages.noVideosFoundDescription": "Commencez à générer des vidéos pour les voir ici.", "historyPages.backToLibrary": "Retour à la bibliothèque", "historyPages.errorLoadingVideo": "Erreur de chargement de la vidéo", "historyPages.loadingVideoDetails": "Chargement des détails de la vidéo...", "historyPages.videoDetails": "Dé<PERSON> de la vidéo", "historyPages.videoInformation": "Informations vidéo", "historyPages.videoNotFound": "La vidéo que vous cherchez n'a pas pu être trouvée ou chargée.", "historyPages.aiContentLibraryTitle": "Bibliothèque de Contenu IA", "historyPages.aiContentLibraryDescription": "<PERSON><PERSON><PERSON><PERSON> et gérez votre contenu généré par l'IA dans différentes catégories.", "demo.notifications.title": "Types de notifications et démonstration de statut", "demo.notifications.description": "Exemples de différents types de notifications avec divers états de statut", "demo.notifications.statusLegend": "Légende des statuts", "demo.speechVoiceSelect.title": "Démo de sélection de voix de synthèse", "demo.speechVoiceSelect.description": "Démonstration du composant réutilisable BaseSpeechVoiceSelectModal avec des props modelValue", "aspectRatio": "Rapport d'aspect", "Image Reference": "Référence d'image", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safety_filter_level": "Niveau de Filtrage de Sécurité", "used_credit": "<PERSON><PERSON><PERSON> utilis<PERSON>", "Safety Filter": "Filtre de sécurité", "safetyFilter.blockLowAndAbove": "Bloquer Bas et Au-dessus", "safetyFilter.blockMediumAndAbove": "Bloquer moyen et supérieur", "safetyFilter.blockOnlyHigh": "Bloc seulement en hauteur", "safetyFilter.blockNone": "<PERSON><PERSON><PERSON>", "historyFilter.all": "<PERSON>ut", "historyFilter.imagen": "Image", "historyFilter.videoGen": "Vidéo Gen", "historyFilter.speechGen": "Génération de discours", "Person Generation": "Génération de Personnes", "downloadImage": "Télécharger l'image", "noImageAvailable": "Aucune image disponible", "enhancePrompt": "Améliorer l'invite", "addImages": "Ajouter des images", "generateVideo": "Générer une vidéo", "happy": "<PERSON><PERSON><PERSON>", "sad": "Triste", "angry": "<PERSON><PERSON><PERSON>", "excited": "Excité", "laughing": "Rire", "crying": "Pleurer", "calm": "Calme", "serious": "Sérieux", "frustrated": "<PERSON><PERSON><PERSON>", "hopeful": "Optimiste", "narrative": "<PERSON><PERSON><PERSON><PERSON>", "kids' storytelling": "Conte pour enfants", "audiobook": "Livre audio", "poetic": "Poétique", "mysterious": "<PERSON>st<PERSON><PERSON>", "inspirational": "Inspirant", "surprised": "<PERSON><PERSON><PERSON>", "confident": "Confiant", "romantic": "Romantique", "scared": "<PERSON><PERSON><PERSON><PERSON>", "trailer voice": "Voix de bande-annonce", "advertising": "Publicité", "documentary": "Documentaire", "newsreader": "Présentateur de nouvelles", "weather report": "Bulletin météorologique", "game commentary": "Commentaire de jeu", "interactive": "Interactif", "customer support": "Support Client", "playful": "<PERSON><PERSON><PERSON>", "tired": "<PERSON><PERSON><PERSON>", "sarcastic": "Sarcastique", "disgusted": "Dégoûté", "whispering": "Chuchotement", "persuasive": "<PERSON><PERSON><PERSON><PERSON>", "nostalgic": "Nostalgique", "meditative": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "announcement": "<PERSON><PERSON><PERSON>", "professional pitch": "Argumentaire professionnel", "casual": "Décontracté", "exciting trailer": "Bande-annonce <PERSON>e", "dramatic": "Dramatique", "corporate": "Entreprise", "tech enthusiast": "Passionné de technologie", "youthful": "<PERSON><PERSON>", "calming reassurance": "Rassurance apaisante", "heroic": "Héroïque", "festive": "Festif", "urgent": "<PERSON><PERSON>", "motivational": "Motivationnel", "friendly": "Amical", "energetic": "Énergique", "serene": "<PERSON><PERSON>", "bold": "Audacieux", "charming": "<PERSON><PERSON><PERSON>", "monotone": "Monotone", "questioning": "Remise en question", "directive": "Directive", "dreamy": "<PERSON><PERSON><PERSON><PERSON>", "epic": "Épique", "lyrical": "Lyrical", "mystical": "Mystique", "melancholy": "Mélancolie", "cheerful": "<PERSON><PERSON>", "eerie": "Surnaturel", "flirtatious": "Flirt.", "thoughtful": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cinematic": "Cinematographique", "humorous": "Humoristique", "instructional": "Pédagogique", "conversational": "Conversationnel", "apologetic": "Désolé", "excuse-making": "Recherche d'excuses", "encouraging": "Encourageant", "neutral": "Neutre", "authoritative": "Autoritaire", "sarcastic cheerful": "Sarcastique enjoué", "reassuring": "<PERSON><PERSON><PERSON><PERSON>", "formal": "Formel", "anguished": "<PERSON><PERSON><PERSON><PERSON>", "giggling": "Ricanement", "exaggerated": "Exagé<PERSON>", "cold": "Froid", "hot-tempered": "Colérique", "grateful": "Reconnaissant", "regretful": "Regrettable", "provocative": "Provocant", "triumphant": "Triomphant", "vengeful": "<PERSON><PERSON><PERSON>", "heroic narration": "Narration héro<PERSON>", "villainous": "<PERSON><PERSON><PERSON>", "hypnotic": "Hypnotique", "desperate": "Désespéré", "lamenting": "Se lamenter", "celebratory": "Célébration", "teasing": "Taquinerie", "exhausted": "<PERSON><PERSON><PERSON><PERSON>", "questioning suspicious": "Remise en question des suspicions", "optimistic": "Optimiste", "bright, gentle voice, expressing excitement.": "Voix claire et douce, exprimant de l'enthousiasme.", "low, slow voice, conveying deep emotions.": "Voix basse et lente, exprimant des émotions profondes.", "sharp, exaggerated voice, expressing frustration.": "Voix aiguë et exagé<PERSON>e, exprimant de la frustration.", "fast, lively voice, full of enthusiasm.": "Voix rapide et vive, pleine d'enthousiasme.", "interrupted, joyful voice, interspersed with laughter.": "Voix interrompue et joyeuse, entrecoupée de rires.", "shaky, low voice, expressing pain.": "Voix tremblante et basse, exprimant la douleur.", "gentle, steady voice, providing reassurance.": "Voix douce et posée, offrant de l'assurance.", "mature, clear voice, suitable for formal content.": "Voix mature et claire, adaptée au contenu formel.", "weary, slightly irritated voice.": "Voix fatiguée, légèrement irritée.", "bright voice, conveying positivity and hope.": "Voix lumineuse, transmettant positivité et espoir.", "natural, gentle voice with a slow rhythm.": "Voix naturelle et douce avec un rythme lent.", "lively, engaging voice, captivating for children.": "Voix vive et engageante, captivante pour les enfants.", "even, slow voice, emphasizing content meaning.": "Voix posée et lente, mettant l'accent sur le sens du contenu.", "rhythmic, emotional voice, conveying subtlety.": "Voix rythmique et émotive, transmettant de la subtilité.", "low, slow voice, evoking curiosity.": "Voix basse et lente, éveillant la curiosité.", "strong, passionate voice, driving action.": "Voix forte et passionnée, incitant à l'action.", "high, interrupted voice, expressing astonishment.": "Voix haute et interrompue, exprimant l'étonnement.", "firm, powerful voice, persuasive and assuring.": "Voix ferme, puissante, persuasive et rassurante.", "sweet, gentle voice, suitable for emotional content.": "Voix douce et suave, adaptée aux contenus émotionnels.", "shaky, interrupted voice, conveying anxiety.": "<PERSON>oi<PERSON> tremblante, entre<PERSON><PERSON><PERSON>, exprimant de l'anxiété.", "deep, strong voice with emphasis, creating suspense.": "Voix profonde et forte avec emphase, créant du suspense.", "engaging, lively voice, emphasizing product benefits.": "Voix engageante et vivante, mettant en avant les avantages du produit.", "formal, clear voice with focus on key points.": "Voix formelle et claire avec un accent sur les points clés.", "calm, profound voice, delivering authenticity.": "Voix calme et profonde, délivrant de l'authenticité.", "standard, neutral voice, clear and precise.": "Voix standard, neutre, claire et précise.", "bright, neutral voice, suitable for concise updates.": "Voix claire et neutre, adaptée aux mises à jour concises.", "fast, lively voice, stimulating excitement.": "Voix rapide et vivante, stimulant l'excitation.", "friendly, approachable voice, encouraging engagement.": "Voix amicale et accessible, encourageant l'engagement.", "empathetic, gentle voice, easy to connect with.": "Voix empathique et douce, facile à connecter.", "clear voice, emphasizing questions and answers.": "<PERSON>oix claire, en mettant l'accent sur les questions et les réponses.", "cheerful, playful voice with a hint of mischief.": "Voix joyeuse et enjouée avec une pointe de malice.", "slow, soft voice lacking energy.": "Voix lente et douce manquant d'énergie.", "ironic, sharp voice, sometimes humorous.": "<PERSON><PERSON>, voix aiguisée, parfois humoristique.", "cold voice, clearly expressing discomfort.": "<PERSON>oix froide, exprimant clairement un malaise.", "soft, mysterious voice, creating intimacy.": "Voix douce et mystérieuse, créant de l'intimité.", "emotional voice, convincing the listener to act.": "Voix émotive, convaincant l'auditeur d'agir.", "gentle voice, evoking feelings of reminiscence.": "<PERSON><PERSON><PERSON> do<PERSON>, évoquant des sentiments de réminiscence.", "even, relaxing voice, suitable for mindfulness.": "Voix même, relaxante, adaptée à la pleine conscience.", "clear voice, emphasizing key words.": "<PERSON>oix claire, en mettant l'accent sur les mots clés.", "confident, clear voice, ideal for business presentations.": "Voix confiante et claire, idéale pour les présentations professionnelles.", "natural, friendly voice, as if talking to a friend.": "Voix naturelle et amicale, comme si l'on parlait à un ami.", "fast, powerful voice, creating tension and excitement.": "Voix rapide et puissante, créant tension et excitation.", "emphasized, suspenseful voice, creating intensity.": "Voix accentuée et pleine de suspense, créant de l'intensité.", "professional, formal voice, suitable for business content.": "Voix professionnelle et formelle, adaptée au contenu commercial.", "energetic, lively voice, introducing new technologies.": "Voix énergique et vivante, introduisant de nouvelles technologies.", "vibrant, cheerful voice, appealing to younger audiences.": "Voix vibrante et joyeuse, qui attire un public plus jeune.", "gentle, empathetic voice, easing concerns.": "Voix douce et empathique, apaisant les inquiétudes.", "strong, decisive voice, full of inspiration.": "Voix forte et décisive, pleine d'inspiration.", "bright, excited voice, suitable for celebrations.": "Voix brillante et enthousiaste, adaptée aux célébrations.", "fast, strong voice, emphasizing urgency.": "Voix rapide et forte, accentuant l'urgence.", "passionate, inspiring voice, encouraging action.": "Voix passionnée et inspirante, encourageant l'action.", "warm, approachable voice, fostering connection.": "Voix chaleureuse et accueillante, favorisant la connexion.", "fast, powerful voice, brimming with enthusiasm.": "Voix rapide et puissante, débordante d'enthousiasme.", "slow, gentle voice, evoking peace and tranquility.": "Voix lente et douce, évoquant paix et tranquillité.", "firm, assertive voice, exuding confidence.": "Voix ferme et assurée, dégageant de la confiance.", "warm, captivating voice, leaving a strong impression.": "Voix chaleureuse et captivante, laissant une forte impression.", "flat, unvaried voice, conveying neutrality or irony.": "Voix plate et uniforme, exprimant la neutralité ou l'ironie.", "curious voice, emphasizing questions.": "Voix curieuse, accentuant les questions.", "firm, clear voice, guiding the listener step-by-step.": "Voix ferme et claire, guidant l'auditeur pas à pas.", "gentle, slow voice, evoking a floating sensation.": "Voix douce et lente, évoquant une sensation de flottement.", "deep, resonant voice, emphasizing grandeur.": "Voix profonde et résonnante, soulignant la grandeur.", "soft, melodic voice, similar to singing.": "Voix douce et mélodieuse, semblable à du chant.", "low, drawn-out voice, evoking mystery.": "Voix basse et prolongée, évoquant le mystère.", "slow, low voice, conveying deep sadness.": "Voix lente et basse, exprimant une profonde tristesse.", "bright, energetic voice, full of positivity.": "Voix lumineuse et énergique, pleine de positivité.", "low, whispery voice, evoking fear or strangeness.": "Voix basse et chuchotante, évoquant la peur ou l'étrangeté.", "sweet, teasing voice, full of allure.": "Voix douce et taquine, pleine de charme.", "slow, reflective voice, full of contemplation.": "Voix lente et réfléchie, pleine de contemplation.", "resonant, emphasized voice, creating a movie-like effect.": "Voix résonnante et accentuée, créant un effet cinématographique.", "lighthearted, cheerful voice, sometimes exaggerated.": "Voix enjouée et joyeuse, parfois exag<PERSON>.", "clear, slow voice, guiding the listener step-by-step.": "Voix claire et lente, guidant l'auditeur étape par étape.", "natural voice, as if chatting with the listener.": "Voix naturelle, comme si l'on discutait avec l'auditeur.", "soft, sincere voice, expressing regret.": "Voix douce et sincère, exprimant des regrets.", "hesitant, uncertain voice, sometimes awkward.": "Voix hésitante, incertaine, parfois maladroite.", "warm voice, providing motivation and support.": "Voix chaleureuse, offrant motivation et soutien.", "even voice, free of emotional bias.": "Voix neutre, sans biais émotionnel.", "strong, powerful voice, exuding credibility.": "Voix forte et puissante, dégageant de la crédibilité.", "cheerful voice with an undertone of mockery.": "Voix enjouée avec un sous-entendu de moquerie.", "gentle, empathetic voice, providing comfort.": "Voix douce et empathique, apportant du réconfort.", "clear, polite voice, suited for formal occasions.": "Voix claire et polie, adaptée aux occasions formelles.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON>, voix tremblante, exprimant la d<PERSON>tresse.", "interrupted voice, mixed with light laughter.": "Voix interrompue, mêlée de légers rires.", "loud, emphasized voice, often humorous.": "Voix forte et accentuée, souvent humoristique.", "flat, unemotional voice, conveying detachment.": "Voix plate et dépourvue d'émotion, transmettant le détachement.", "fast, sharp voice, sometimes out of control.": "Voix rapide et aiguë, parfois incontrôlable.", "warm, sincere voice, expressing appreciation.": "Voix chaude et sincère, exprimant de la reconnaissance.", "low, subdued voice, full of remorse.": "Voix basse et feutrée, pleine de remords.", "challenging, strong voice, full of insinuation.": "Exigeant, voix forte, pleine d'insinuations.", "loud, powerful voice, full of victory.": "Voix forte et puissante, pleine de victoire.", "low, cold voice, expressing determination for revenge.": "Voix basse et froide, exprimant une détermination à se venger.", "strong, inspiring voice, emphasizing heroic deeds.": "Voix forte et inspirante, soulignant les actes héroïques.", "low, drawn-out voice, full of scheming.": "Voix basse et traînante, pleine de manigances.", "even, repetitive voice, drawing the listener in.": "Voix égale et répétitive, captivant l'auditeur.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON>, voix tremblante, exprimant le désespoir.", "low, sorrowful voice, as if mourning.": "Voix basse et plaintive, comme un deuil.", "excited, joyful voice, full of festive spirit.": "Voix excitée, joyeuse, pleine d'esprit festif.", "light, playful voice, sometimes mockingly.": "Voix légère et enjouée, parfois moqueuse.", "weak, broken voice, expressing extreme fatigue.": "Voix faible et brisée, exprimant une fatigue extrême.", "slow, emphasized voice, full of suspicion.": "<PERSON>oix lente, appuy<PERSON>, pleine de suspicion.", "bright, hopeful voice, creating positivity.": "Voix lumineuse et pleine d'espoir, créant de la positivité.", "Your audio will be processed with the latest stable model.": "Votre audio sera traité avec le dernier modèle stable.", "Your audio will be processed with the latest beta model.": "Votre audio sera traité avec le dernier modèle bêta.", "You don't have any saved prompts yet.": "Vous n'avez pas encore de invites enregistrées.", "Commercial Use": "Usage commercial", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes. However, you may not resell, redistribute, or sublicense the speech output without prior written consent from Text To Speech OpenAI.": "Vous avez le droit d'utiliser la sortie vocale générée par nos services à des fins personnelles, éducatives ou commerciales. Cependant, vous ne pouvez pas revendre, redistribuer ou accorder une sous-licence de la sortie vocale sans le consentement écrit préalable de Text To Speech OpenAI.", "Other people’s privacy": "La vie privée des autres", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "Vous devez respecter la vie privée des autres lors de l'utilisation de nos services. Ne téléchargez pas et ne créez pas de sorties vocales contenant des informations personnelles, des données confidentielles ou du matériel protégé par des droits d'auteur sans permission.", "{price}$ per credit": "{price}$ par crédit", "Pricing": "Tarification", "Simple and flexible. Only pay for what you use.": "Simple et flexible. Payez uniquement ce que vous consommez.", "Pay as you go": "Paiement à l'utilisation", "Flexible": "Flexible", "Input characters": "Caractères d'entrée", "Audio model": "Modèle audio", "Credits": "Crédits", "Cost": "Coût", "HD quality voices": "Voix de qualité HD", "Advanced model": "<PERSON><PERSON><PERSON><PERSON>", "Buy now": "Achetez maintenant", "Paste your text to calculate": "Collez votre texte pour calculer", "Paste your text here...": "Collez votre texte ici...", "Calculate": "Calculer", "Estimate your cost by drag the slider below or": "Estimez votre coût en faisant glisser le curseur ci-dessous ou", "calming": "A<PERSON><PERSON>t", "customer": "Client", "exciting": "passionnant", "excuse": "Excuse-moi", "game": "<PERSON><PERSON>", "hot": "<PERSON><PERSON>", "kids": "<PERSON><PERSON><PERSON>", "professional": "Professionnel", "tech": "Technologie", "trailer": "Remorque", "weather": "Temps", "No thumbnail available": "Miniature non disponible", "Debit or Credit Card": "<PERSON><PERSON> de <PERSON> ou de cré<PERSON>", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express et plus encore", "Top up now": "Rechargez maintenant", "noVideoAvailable": "Aucune vidéo disponible", "common.back": "Retour", "common.edit": "Modifier", "common.save": "<PERSON><PERSON><PERSON><PERSON>", "common.cancel": "Annuler", "common.delete": "<PERSON><PERSON><PERSON><PERSON>", "common.copy": "<PERSON><PERSON><PERSON>", "common.copied": "<PERSON><PERSON><PERSON>", "common.manage": "<PERSON><PERSON><PERSON>", "aiToolMenu.textToImage": "Texte en image", "profileMenu.integration": "Intégration", "videoTypes.examples.tikTokDanceTrend": "Tendance de danse TikTok", "videoTypes.examples.energeticDanceDescription": "Vidéo de danse énergique avec des couleurs vives, des coupures rapides, de la musique tendance, format vertical, style réseau social", "videoTypes.examples.instagramReel": "<PERSON><PERSON>", "videoTypes.examples.lifestyleDescription": "Contenu de style de vie avec des visuels esthétiques, des transitions fluides, des effets tendance, un récit captivant", "videoTypes.examples.comedySketch": "Sketch comique", "videoTypes.examples.funnyComedyDescription": "Scène comique amusante avec des personnages expressifs, des situations humoristiques, des dialogues divertissants, ambiance légère.", "videoTypes.examples.productLaunchAd": "Annonce de lancement de produit", "videoTypes.examples.professionalCorporateDescription": "Vidéo professionnelle d'entreprise avec présentation exécutive, environnement de bureau propre, style formel d'affaires", "videoTypes.examples.quickPromoVideo": "Vidéo promo rapide", "videoTypes.examples.fastPacedPromoDescription": "Contenu promotionnel dynamique avec une production efficace, des visuels économiques, un message simplifié.", "videoTypes.examples.birthdayGreeting": "Salutation d'anniversaire", "videoTypes.examples.personalizedBirthdayDescription": "Vidéo d'anniversaire personnalisée avec des décorations festives, un éclairage chaleureux, une ambiance de fête, un message sincère.", "videoTypes.examples.brandStoryVideo": "Vidéo de l'histoire de la marque", "videoTypes.examples.tutorialVideo": "<PERSON><PERSON><PERSON><PERSON>", "videoTypes.examples.manOnThePhone": "Homme au téléphone", "videoTypes.examples.runningSnowLeopard": "Course du léopard des neiges", "videoTypes.examples.snowLeopard": "Léopard des neiges", "videoTypes.styles.cartoonAnimated": "Vidéo de style dessin animé ou animé", "videoTypes.styles.naturalDocumentary": "Séquences de type documentaire naturel", "videoTypes.styles.naturalLifelike": "Style vidéo naturel et réaliste", "videoTypes.styles.professionalMovieQuality": "Qualité professionnelle digne d'un film avec un éclairage dramatique", "videoTypes.styles.creativeStylized": "Effets vidéo créatifs et stylisés", "videoTypes.styles.retroVintage": "Esthétique vidéo r<PERSON>tro ou vintage", "historyFilter.dialogueGen": "Dialogue Gen", "historyFilter.speechGenDocument": "Génération de discours à partir du document", "demo.notifications.availableNotificationTypes": "Types de notifications disponibles", "demo.speechVoiceSelect.example1": "Exemple 1 : Utilisation par défaut", "demo.speechVoiceSelect.example2": "Exemple 2 : <PERSON><PERSON> <PERSON><PERSON>", "demo.speechVoiceSelect.example3": "Exemple 3 : <PERSON> taille", "demo.speechVoiceSelect.example4": "Exemple 4 : Plusieurs exemples d'erreurs", "demo.speechVoiceSelect.example5": "Exemple 5 : Comparaison des statuts", "demo.speechVoiceSelect.mainNarrator": "Narrateur principal", "demo.speechVoiceSelect.characterVoice": "Voix de personnage", "demo.speechVoiceSelect.selectedVoicesSummary": "Résumé des voix sélectionnées :", "demo.speechVoiceSelect.clearAll": "Tout effacer", "demo.speechVoiceSelect.setRandomVoices": "Définir des Voix Aléatoires", "demo.speechVoiceSelect.logToConsole": "Journaliser dans la console", "demo.speechVoiceSelect.notSelected": "Non sélectionné", "demo.speechVoiceSelect.voiceSelectionsChanged": "Sélections vocales modifiées", "demo.historyWrapper.title": "Démo du Badge d'État de HistoryWrapper", "demo.historyWrapper.normalStatus": "Exemple 1 : Statut Normal (statut = 1)", "demo.historyWrapper.processingStatus": "Exemple 2 : Traitement en cours (statut = 2)", "demo.historyWrapper.errorStatus": "Exemple 3 : <PERSON><PERSON> d'erreur (statut = 3) - Affiche le badge d'erreur", "demo.historyWrapper.multipleErrorExamples": "Exemple 4 : Multiples exemples d'erreurs", "demo.historyWrapper.statusComparison": "Exemple 5 : Comparaison de l'état", "demo.historyWrapper.normalImageGeneration": "Génération d'images normale", "demo.historyWrapper.videoGenerationInProgress": "Génération de vidéo en cours", "demo.historyWrapper.speechGenerationFailed": "Échec de la génération de discours", "demo.historyWrapper.imageFailed": "Image échouée", "demo.historyWrapper.videoFailed": "Échec de la vidéo", "demo.historyWrapper.speechFailed": "Discours échoué", "demo.historyWrapper.statusSuccess": "Statut : Succès", "demo.historyWrapper.statusProcessing": "Statut : Traitement", "demo.historyWrapper.statusError": "État : <PERSON><PERSON><PERSON>", "demo.historyWrapper.status1Success": "Statut 1 : Succès", "demo.historyWrapper.status2Processing": "Statut 2 : En cours de traitement", "demo.historyWrapper.badgeBehavior": "Comportement des badges :", "demo.historyWrapper.showsOnlyTypeAndStyle": "Montre uniquement les badges de type et de style", "demo.historyWrapper.showsTypeStyleAndError": "<PERSON>re le type, le style, et l'insigne d'erreur rouge avec une icône d'alerte.", "demo.historyWrapper.redBackgroundWithWhite": "Fond rouge avec texte blanc et icône de cercle d'alerte", "demo.historyWrapper.allBadgesHideOnHover": "Tous les badges se masquent au survol pour afficher le contenu de la superposition.", "demo.speechVoiceCaching.title": "Test de mise en cache de la voix parlée", "demo.speechVoiceCaching.description": "Test pour vérifier la mise en cache des voix entre différents composants.", "demo.speechVoiceCaching.component1Modal": "Composant 1 - Modal", "demo.speechVoiceCaching.component3RegularSelect": "Composant 3 - Sélection régulière", "demo.speechVoiceCaching.forceReloadVoices": "Recharger les voix de force", "demo.speechVoiceCaching.clearAllSelections": "Effacer toutes les sélections", "demo.speechVoiceCaching.logStoreState": "État du magasin de journaux", "demo.speechVoiceCaching.refreshPageInstructions": "Actualisez la page et ouvrez n'importe quel composant - cela se rechargera.", "demo.speechVoiceCaching.checkNetworkTab": "Vérifiez l'onglet Réseau pour confirmer les appels API", "demo.speechVoiceCaching.selectedVoicePersist": "La voix sélectionnée sera conservée via le localStorage.", "demo.speechVoiceCaching.pageMounted": "<PERSON> mon<PERSON>, le magasin s'initialisera automatiquement si nécessaire.", "integration.title": "Intégration", "integration.subtitle": "<PERSON><PERSON><PERSON> vos clés d'API et paramètres d'intégration.", "integration.apiKeys": "Clés API", "integration.apiKeysDescription": "G<PERSON>rez vos clés API pour un accès programmatique.", "integration.webhook": "Webhook", "integration.webhookDescription": "Configurer l'URL du webhook pour les notifications", "apiKeys.title": "Clés API", "apiKeys.subtitle": "G<PERSON>rez vos clés API pour un accès programmatique.", "apiKeys.create": "<PERSON><PERSON><PERSON> une clé API", "apiKeys.createNew": "<PERSON><PERSON>er une nouvelle clé API", "apiKeys.createFirst": "<PERSON><PERSON>er une première clé API", "apiKeys.name": "Nom", "apiKeys.nameDescription": "Donnez à votre clé API un nom descriptif.", "apiKeys.namePlaceholder": "par exemple, clé API de mon application", "apiKeys.nameRequired": "Le nom de la clé API est requis.", "apiKeys.createdAt": "<PERSON><PERSON><PERSON>", "apiKeys.noKeys": "Pas de clés API", "apiKeys.noKeysDescription": "Créez votre première clé API pour commencer à accéder de manière programmatique.", "apiKeys.created": "Clé API créée avec succès", "apiKeys.createError": "Échec de la création de la clé API", "apiKeys.deleted": "Clé API supprimée avec succès", "apiKeys.deleteError": "Échec de la suppression de la clé API", "apiKeys.deleteConfirm": "Supprimer la clé API", "apiKeys.deleteWarning": "Êtes-vous sûr de vouloir supprimer cette clé API ? Cette action est irréversible.", "apiKeys.copied": "Clé API copiée dans le presse-papiers", "apiKeys.copyError": "Échec de la copie de la clé API", "webhook.title": "Configuration de Webhook", "webhook.subtitle": "Configurer l'URL du webhook pour les notifications en temps réel", "webhook.configuration": "URL de webhook", "webhook.currentUrl": "URL de Webhook actuel", "webhook.currentUrlDescription": "Cette URL recevra des requêtes POST pour les événements webhook.", "webhook.notConfigured": "Aucune URL de webhook configurée", "webhook.url": "URL du Webhook", "webhook.urlDescription": "Entrez l'URL où vous souhaitez recevoir les notifications de webhook.", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "Veuillez d'abord entrer une URL de webhook.", "webhook.invalidUrl": "Veuillez saisir une URL valide.", "webhook.saved": "URL du webhook enregistré avec succès.", "webhook.saveError": "Échec de l'enregistrement de l'URL du webhook.", "webhook.test": "Test", "webhook.testSent": "Message de test", "webhook.testDescription": "Test du webhook envoy<PERSON> avec succès.", "webhook.information": "Informations sur le webhook", "webhook.howItWorks": "Comment ça fonctionne", "webhook.description": "Lorsqu'il est configuré, nous enverrons des requêtes HTTP POST à votre URL de webhook chaque fois que certains événements se produisent dans votre compte.", "webhook.events": "Événements Webhook", "webhook.imageGenerated": "Génération d'image terminée", "webhook.imageGenerationFailed": "La génération d'image a échoué.", "webhook.creditUpdated": "Solde du crédit mis à jour", "webhook.payloadFormat": "Format de charge utile", "webhook.payloadDescription": "Les requêtes Webhook seront envoyées sous forme de JSON avec la structure suivante :", "webhook.security": "Sécurité", "webhook.securityDescription": "Nous recommandons d'utiliser des URL HTTPS et de mettre en œuvre la vérification des signatures pour garantir l'authenticité des webhooks.", "error.general": "<PERSON><PERSON><PERSON>", "error.validation": "Erreur de validation", "error.required": "Champ obligatoire", "success.saved": "Enregistré avec succès", "success.created": "<PERSON><PERSON><PERSON> avec succès", "success.deleted": "Supprimé avec succès", "success.copied": "Copié dans le presse-papiers", "confirmDelete": "Confirmer la <PERSON>", "confirmDeleteDescription": "Êtes-vous sûr de vouloir supprimer cet article ? Cette action est irréversible.", "historyDeleted": "Élément d'historique supprimé avec succès.", "deleteError": "Échec de la suppression de l'élément historique", "Regenerate Image": "Régénérer l'image", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "Vous n'avez apporté aucune modification aux paramètres. Êtes-vous sûr de vouloir régénérer la même image ?", "Yes, Regenerate": "<PERSON><PERSON>, Régénérer", "Cancel": "Annuler", "models.imagen4Fast": "Imagen 4 Rapide", "models.imagen4Ultra": "Imagen 4 Ultra", "voiceTypes.favoriteVoices": "Voix préférées", "voiceTypes.geminiVoices": "Voix des Gémeaux", "speech.dialogueGeneration.complete": "Génération de dialogue terminée", "speech.dialogueGeneration.failed": "Échec de la génération du dialogue", "speech.dialogueGeneration.pending": "Génération de Dialogue en Attente", "speech.dialogueGeneration.dialogueGen": "Génération de dialogue", "speech.dialogueGeneration.successMessage": "Votre dialogue a été généré avec succès.", "speech.speechGeneration.complete": "Génération de discours terminée", "speech.speechGeneration.failed": "Échec de la génération de discours", "speech.speechGeneration.pending": "Génération de discours en attente", "speech.speechGeneration.successMessage": "Votre discours a été généré avec succès.", "speech.speechGeneration.requestWaiting": "Votre demande de génération de discours est en attente de traitement.", "speech.errors.failedToLoadEmotions": "Échec du chargement des émotions", "tts-document": "Fichier à la parole", "assignVoicesToSpeakers": "Attribuer des voix aux interlocuteurs", "speakers": "Haut-parleurs", "addSpeaker": "Ajouter un intervenant", "noVoiceAssigned": "Aucune voix assignée", "noSpeakersAdded": "Aucun intervenant ajouté pour le moment", "assignVoiceToSpeaker": "Attribuer une voix à {speaker}", "assigned": "Attribué", "assign": "Attribuer", "editSpeaker": "Modifier le locuteur", "speakerName": "Nom du conférencier", "enterSpeakerName": "Entrez le nom de l'orateur", "save": "<PERSON><PERSON><PERSON><PERSON>", "speaker": "Conférencier", "assignVoices": "Attribuer des voix", "speakersWithVoices": "{assigned}/{total} orateurs ont des voix", "dialogs": "Dialogues", "addDialog": "Ajouter une boîte de dialogue", "enterDialogText": "Entrez le texte du dialogue...", "selectSpeaker": "Sé<PERSON><PERSON><PERSON> le haut-parleur", "generateDialogSpeech": "Générer un discours dialogué", "voice 1": "Voix 1", "voice 2": "Voix 2", "uuid": "UUID", "output_format": "Format de sortie", "output_channel": "Canal de sortie", "file_name": "Nom de fi<PERSON>er", "file_size": "<PERSON><PERSON>", "speakers_count": "Nombre de haut-parleurs", "custom_prompt": "<PERSON><PERSON><PERSON>", "Please wait a moment...": "Veuillez patienter un instant...", "Click to copy": "Cliquer pour copier", "Copied to clipboard": "Copié dans le presse-papiers", "UUID has been copied to clipboard": "L'UUID a été copié dans le presse-papiers.", "Credits: {credits} remaining": "Crédits : {credits} restants", "This generation will cost: {cost} Credits": "Cette génération coûtera : {cost} Crédits", "Your generated video will appear here": "Votre vidéo générée apparaîtra ici.", "Regenerate Video": "Régénérer la vidéo", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "Vous n'avez apporté aucune modification aux paramètres. Êtes-vous sûr de vouloir régénérer la même vidéo ?", "Your generated speech will appear here": "Votre discours généré apparaîtra ici.", "Regenerate Speech": "Régénérer le discours", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "Vous n'avez apporté aucun changement aux paramètres. Êtes-vous sûr de vouloir régénérer le même discours ?", "Generated Speech": "Discours généré", "Generating speech...": "Génération de la parole...", "View Details": "Voir les détails", "Speech Examples": "Exemples de discours", "Click on any example to use its prompt for speech generation": "Cliquez sur n'importe quel exemple pour utiliser son invite pour la génération de discours.", "Click to use": "Cliquez pour utiliser", "videoStyles.selectVideoStyle": "Sélectionner le style vidéo", "videoStyles.cinematic": "Cinematic", "videoStyles.realistic": "<PERSON><PERSON><PERSON><PERSON>", "videoStyles.animated": "<PERSON><PERSON><PERSON>", "videoStyles.artistic": "Artistique", "videoStyles.documentary": "Documentaire", "videoStyles.vintage": "<PERSON><PERSON><PERSON>", "ui.buttons.downloadApp": "Télécharger l'application", "ui.buttons.signUp": "S'inscrire", "ui.buttons.viewDetails": "Voir les détails", "ui.buttons.seeLater": "À plus tard", "ui.buttons.selectFile": "Sélectionner un fichier", "ui.buttons.selectFiles": "Sélectionner des fichiers", "ui.buttons.pickAVoice": "Choisissez une voix", "ui.buttons.topUpNow": "Rechargez maintenant", "ui.buttons.pressEscToClose": "Appuyez sur ÉCHAP pour fermer", "ui.labels.clickToCopy": "Cliquez pour copier", "ui.labels.copiedToClipboard": "Copié dans le presse-papiers", "ui.labels.noAudioAvailable": "Aucun audio disponible", "ui.labels.noThumbnailAvailable": "Aucune miniature disponible", "ui.labels.noPromptAvailable": "Aucune invite disponible.", "ui.labels.videoModel": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "ui.labels.speechModel": "<PERSON><PERSON><PERSON><PERSON> de discours", "ui.labels.generatedSpeech": "Discours généré", "ui.labels.defaultVoice": "Voix par défaut", "ui.labels.selectAnyVoice": "Sélectionnez une voix", "ui.labels.cameraMotion": "Mouvement de caméra", "ui.labels.transform": "Transformer", "ui.labels.transforming": "Transformation...", "ui.messages.imageLoaded": "Image chargée", "ui.messages.imageRevealComplete": "Image révélée intégralement.", "ui.messages.processingImage": "Traitement de l'image", "ui.messages.videoLoaded": "<PERSON><PERSON><PERSON><PERSON> char<PERSON>", "ui.messages.videoProcessing": "Traitement vidéo", "ui.messages.invalidDownloadLink": "Lien de téléchargement invalide", "ui.messages.pleaseSelectSupportedFile": "Veuillez sélectionner un fichier pris en charge.", "ui.messages.deleteConfirm": "Confirmer la <PERSON>", "ui.messages.deleteFailed": "Suppression échouée", "ui.messages.youHaveNewNotification": "Vous avez une nouvelle notification.", "ui.messages.yourGenerationIsReady": "Votre génération est prête", "ui.errors.errorLoadingImage": "Erreur de chargement de l'image :", "ui.errors.failedToCopy": "Échec de la copie :", "ui.errors.failedToPlayAudioPreview": "Échec de la lecture de l'aperçu audio :", "ui.errors.wavesurferError": "<PERSON><PERSON><PERSON> :", "ui.errors.somethingWentWrong": "<PERSON><PERSON><PERSON> chose s'est mal passé. Veuillez réessayer.", "ui.errors.supabaseUrlRequired": "L'URL de Supabase et la clé anonyme sont nécessaires.", "dialog.startTypingHere": "Commencez à taper le dialogue ici...", "payment.debitCreditCard": "<PERSON><PERSON> de <PERSON> ou de cré<PERSON>", "payment.cardDescription": "Visa, Mastercard, American Express et plus encore", "Style Description": "Description du style", "Dialog Content": "Contenu du dialogue", "Your generated dialog will appear here": "Votre dialogue généré apparaîtra ici.", "Regenerate Dialog": "R<PERSON><PERSON>n<PERSON><PERSON> le dialogue", "Generated Dialog": "<PERSON> gén<PERSON>", "Generating dialog...": "Génération de dialogue...", "Dialog Information": "Informations de dialogue", "Audio Player": "Lecteur audio", "Voices": "Voix", "Voice 1": "Voix 1", "Voice 2": "Voix 2", "Dialog Examples": "Exemples de dialogue", "Click on any example to use its style or dialog content": "Cliquez sur un exemple pour utiliser son style ou son contenu de dialogue.", "Use Style": "Utiliser le style", "Use Dialog": "Utiliser le dialogue", "personGeneration": "Génération de personnes", "Imagen": "Image", "On": "On", "Off": "Désactivé", "Prompts will always be refined to improve output quality": "<PERSON> invites seront toujours affinées pour améliorer la qualité des résultats.", "Prompts will not be modified": "Les invites ne seront pas modifiées.", "Tips": "Conseils", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Votre vidéo est toujours en cours de génération en arrière-plan. Vous pouvez fermer cette page et vérifier l'onglet historique pour la vidéo générée et nous vous informerons lorsqu'elle sera prête.", "Go to History": "Aller à l'Histoire", "footer.youtube": "Youtube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Texte en parole OpenAI", "footer.privacyPolicy": "Politique de confidentialité", "footer.termsOfService": "Conditions d'utilisation", "footer.terms": "Conditions", "footer.privacy": "Confidentialité", "Generate": "<PERSON><PERSON><PERSON><PERSON>", "Prompt": "Invite", "Generate Video": "Générer une vidéo", "ui.errors.generationFailed": "Échec de génération"}